import React from 'react';
import { Space } from 'antd';
import GeneralInformation from './GeneralInformation';

// 示例：如何使用 GeneralInformation 组件的不同模式

const GeneralInformationExample: React.FC = () => {
  // 示例数据
  const sampleData = {
    name: 'Alcohol Consumption Warning',
    description: 'The sale and consumption of alcoholic beverages are prohibited for minors. Drink responsibly and be aware of local regulations.',
    status: true,
    legalDocument: {
      filename: 'alcohol_legal_approval.pdf',
      hash: 'sample_hash_123'
    }
  };

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* 编辑模式 - 显示表单和编辑按钮 */}
        <div>
          <h3>编辑模式 (isEdit = true)</h3>
          <p>显示表单输入控件，用户可以编辑内容</p>
          <GeneralInformation 
            isEdit={true}
            initialValues={sampleData}
          />
        </div>

        {/* 只读模式 - 显示纯文本 */}
        <div>
          <h3>只读模式 (isEdit = false)</h3>
          <p>显示纯文本内容，不可编辑</p>
          <GeneralInformation 
            isEdit={false}
            initialValues={sampleData}
          />
        </div>

        {/* 空数据的只读模式 */}
        <div>
          <h3>空数据的只读模式</h3>
          <p>当没有数据时显示 N/A</p>
          <GeneralInformation 
            isEdit={false}
            initialValues={{}}
          />
        </div>

      </Space>
    </div>
  );
};

export default GeneralInformationExample;

/**
 * 使用说明：
 * 
 * 1. isEdit = true: 
 *    - 显示表单输入控件
 *    - 显示编辑/保存/取消按钮
 *    - 用户可以编辑内容
 * 
 * 2. isEdit = false:
 *    - 显示纯文本内容
 *    - 不显示编辑按钮
 *    - 内容不可编辑
 * 
 * 3. 可复用性：
 *    - 通过 isEdit 参数控制显示模式
 *    - 通过 initialValues 传入数据
 *    - 组件内部处理编辑状态切换
 *    - 适用于详情页、编辑页等不同场景
 */
