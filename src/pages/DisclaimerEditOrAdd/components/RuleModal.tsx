import {
  Button,
  message,
  Modal,
  Space,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';

import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import AttributeRuleTable from './AttributeRuleTable';
import BasicCategoryCascader from './BasicCategoryCascader';

const { Text } = Typography;

interface RuleModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void;
  availableCategories: uploadAdmin.ICategory[];
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  usedCategoryIds: number[];
  onCategoryChange: (categoryId: number) => void;
  // 编辑模式的props
  editingRule?: disclaimer.IUpdateDisclaimerConditionRequest;
  mode: 'add' | 'edit';
}

const RuleModal: React.FC<RuleModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  availableCategories,
  categoryAttributes,
  categoriesLoading,
  usedCategoryIds,
  onCategoryChange,
  editingRule,
  mode,
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
  const [attributeRules, setAttributeRules] = useState<disclaimer.IAttrCondition[]>([]);
  const [initialCategoryId, setInitialCategoryId] = useState<number | undefined>();

  // 初始化状态
  useEffect(() => {
    if (visible) {
      if (mode === 'edit' && editingRule) {
        setSelectedCategoryId(editingRule.catId);
        setInitialCategoryId(editingRule.catId);
        setAttributeRules(editingRule.attrConditionInfo?.attrConditionList || []);
      } else {
        setSelectedCategoryId(undefined);
        setInitialCategoryId(undefined);
        setAttributeRules([]);
      }
    }
  }, [visible, editingRule, mode]);

  // 处理category选择变化
  const handleCategoryChange = (categoryId: number) => {
    if (categoryId !== selectedCategoryId) {
      // 切换category时清空attribute rules
      setAttributeRules([]);
      onCategoryChange(categoryId);
    }
    setSelectedCategoryId(categoryId);
  };

  // 处理attribute rule添加
  const handleAddAttributeRule = (attributeRule: disclaimer.IAttrCondition) => {
    setAttributeRules(prev => [...prev, attributeRule]);
  };

  // 处理attribute rule编辑
  const handleEditAttributeRule = (index: number, attributeRule: disclaimer.IAttrCondition) => {
    setAttributeRules(prev => {
      const newRules = [...prev];
      newRules[index] = attributeRule;
      return newRules;
    });
  };

  // 处理attribute rule删除
  const handleDeleteAttributeRule = (index: number) => {
    setAttributeRules(prev => prev.filter((_, i) => i !== index));
  };

  // 处理确认 - 创建只有category的规则
  const handleCreateRuleOnly = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category first');
      return;
    }

    const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
      catId: selectedCategoryId,
      attrConditionInfo: {
        attrConditionList: [], // 空的attribute rules
      },
    };

    // 如果是编辑模式，保留conditionId
    if (mode === 'edit' && editingRule?.conditionId) {
      newRule.conditionId = editingRule.conditionId;
    }

    onConfirm(newRule);
  };

  // 处理确认 - 创建规则（包含attribute rules）
  const handleCreateRuleWithAttributes = () => {
    if (!selectedCategoryId) {
      message.warning('Please select a category first');
      return;
    }

    const newRule: disclaimer.IUpdateDisclaimerConditionRequest = {
      catId: selectedCategoryId,
      attrConditionInfo: {
        attrConditionList: attributeRules,
      },
    };

    // 如果是编辑模式，保留conditionId
    if (mode === 'edit' && editingRule?.conditionId) {
      newRule.conditionId = editingRule.conditionId;
    }

    onConfirm(newRule);
  };

  // 获取选中category的名称
  const getSelectedCategoryName = () => {
    if (!selectedCategoryId) return '';
    const category = availableCategories.find(cat => cat.catId === selectedCategoryId);
    return category?.catName || '';
  };

  // 获取可用的categories（排除已使用的）
  const getAvailableCategories = () => {
    console.log('usedCategoryIds', usedCategoryIds)
    if (mode === 'edit' && editingRule) {
      // 编辑模式：排除已使用的，但包含当前的
      return availableCategories.filter(cat =>
        !usedCategoryIds.includes(cat.catId!) || cat.catId === editingRule.catId
      );
    } else {
      // 添加模式：排除所有已使用的
      return availableCategories.filter(cat => !usedCategoryIds.includes(cat.catId!));
    }
  };

  // 检查是否有变化（仅编辑模式）
  const hasChanges = () => {
    if (mode === 'add') return true;

    const categoryChanged = selectedCategoryId !== initialCategoryId;
    const attributesChanged = JSON.stringify(attributeRules) !==
      JSON.stringify(editingRule?.attrConditionInfo?.attrConditionList || []);
    return categoryChanged || attributesChanged;
  };

  // 获取Modal标题
  const getModalTitle = () => {
    return mode === 'add' ? 'Add New Rule' : 'Edit Category and Attribute Rules';
  };

  return (
    <Modal
      title={getModalTitle()}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <div style={{ padding: '16px 0' }}>
        {/* Step 1: Select Category */}
        <div style={{ marginBottom: 24 }}>
          <Text strong style={{ display: 'block', marginBottom: 8 }}>
            {mode === 'add' ? 'Step 1: Select Category *' : 'Associated Category *'}
          </Text>
          <BasicCategoryCascader
            categories={getAvailableCategories()}
            value={selectedCategoryId}
            onChange={handleCategoryChange}
            placeholder="Select category (leaf nodes only)"
            multiple={false}
            loading={categoriesLoading}
            disabled={categoriesLoading}
            style={{ width: '100%' }}
          />
          {getAvailableCategories().length === 0 && (
            <Text type="secondary" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
              All categories are already used. Please remove a rule first.
            </Text>
          )}
          {selectedCategoryId && (
            <Text type="secondary" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
              Selected: <Text strong>{getSelectedCategoryName()}</Text>
              {mode === 'edit' && selectedCategoryId !== initialCategoryId && (
                <Text type="warning" style={{ marginLeft: 8 }}>
                  (Category changed - attribute rules have been cleared)
                </Text>
              )}
            </Text>
          )}
        </div>

        {/* Step 2: Attribute Rules */}
        {selectedCategoryId && (
          <div style={{ marginBottom: 24 }}>
            <Text strong style={{ display: 'block', marginBottom: 8 }}>
              {mode === 'add' ? 'Step 2: Add Attribute Rules (Optional)' : 'Attribute Rules'}
            </Text>

            <AttributeRuleTable
              attributeRules={attributeRules}
              categoryAttributes={categoryAttributes.get(selectedCategoryId) || []}
              onAdd={handleAddAttributeRule}
              onEdit={handleEditAttributeRule}
              onDelete={handleDeleteAttributeRule}
            />
          </div>
        )}

        {/* Changes Summary (Edit mode only) */}
        {mode === 'edit' && hasChanges() && (
          <div style={{
            marginBottom: 16,
            padding: 12,
            backgroundColor: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: 6
          }}>
            <Text type="warning" strong>Changes detected:</Text>
            <ul style={{ margin: '8px 0 0 16px', padding: 0 }}>
              {selectedCategoryId !== initialCategoryId && (
                <li>Category will be changed to: <Text strong>{getSelectedCategoryName()}</Text></li>
              )}
              {JSON.stringify(attributeRules) !== JSON.stringify(editingRule?.attrConditionInfo?.attrConditionList || []) && (
                <li>Attribute rules have been modified ({attributeRules.length} rules)</li>
              )}
            </ul>
          </div>
        )}

        {/* Footer Actions */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          paddingTop: 16,
          borderTop: '1px solid #f0f0f0'
        }}>
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Space>
            <Button
              type="default"
              onClick={handleCreateRuleOnly}
              disabled={!selectedCategoryId}
            >
              {mode === 'add' ? 'Create Rule Only' : 'Save Category Only'}
            </Button>
            <Button
              type="primary"
              onClick={handleCreateRuleWithAttributes}
              disabled={!selectedCategoryId}
            >
              {mode === 'add'
                ? (attributeRules.length > 0 ? `Create Rule with ${attributeRules.length} Attribute${attributeRules.length > 1 ? 's' : ''}` : 'Create Rule')
                : 'Save Changes'
              }
            </Button>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default RuleModal;
