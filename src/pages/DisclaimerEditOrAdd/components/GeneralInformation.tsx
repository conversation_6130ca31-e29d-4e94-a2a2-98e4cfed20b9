import {
  Button,
  Form,
  Input,
  message,
  Switch,
} from 'antd';
import React, {
  useMemo,
  useState,
} from 'react';

import {
  addDisclaimer,
  updateDisclaimer,
} from 'src/api/disclaimer';
import { DisclaimerStatus } from 'src/api/disclaimer/constants';
import type { disclaimer } from 'src/api/disclaimer/disclaimer';
import ConfigurableCard from 'src/components/ConfigurableCard';
import UploadFile from 'src/components/Upload';
import style from '../style.module.scss';

const { TextArea } = Input;

// 表单验证规则
export const validationRules = {
  disclaimerName: [
    { required: true, message: 'Please enter disclaimer name' },
    { max: 100, message: 'Disclaimer name cannot exceed 100 characters' },
    { whitespace: true, message: 'Disclaimer name cannot be empty' },
  ],
  disclaimerDescription: [
    { required: true, message: 'Please enter disclaimer description' },
    { max: 1000, message: 'Description cannot exceed 1000 characters' },
    { whitespace: true, message: 'Description cannot be empty' },
  ],
};

interface GeneralInformationProps {
  value?: disclaimer.IDisclaimer;
  onChange?: (value: disclaimer.IDisclaimer) => void;
}

const FormInput = ({
    value,
    onChange,
    children,
    isEditing,
    textChildren,
  }: {
    isEditing?: boolean;
    value?: any;
    onChange?: (value: any) => void;
    children: JSX.Element;
    textChildren?: (value: any) => JSX.Element;
  },
) => {
  return <div>
    {
      (isEditing || !textChildren) ? React.cloneElement(children, { value, onChange }) : textChildren(value)
    }
  </div>
}

const GeneralInformation = ({
  value = {
    name: '',
    description: '',
    disclaimerStatus: DisclaimerStatus.DISCLAIMER_DISABLED,
    approvalFile: undefined,
  },
  onChange,
}: GeneralInformationProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(!value?.id);

  const isDetailPage = useMemo(() => {
    return !!value?.id;
  }, [value]);

  const cancel = () => {
    setIsEditing(false);
    form.setFieldsValue(value)
  }
  // 保存General Information
  const handleSave = async () => {
    try {
      // 验证基础字段
      const value = await form.validateFields();

      setLoading(true);
      if (isDetailPage) {
        const res = await updateDisclaimer(value);
        if (!res?.debugMsg) {
          onChange?.({
            ...value,
          });
          setIsEditing(false);
        } else {
          message.error('Failed to update disclaimer');
        }
      } else {
        const res = await addDisclaimer(value);
        if (res?.id) {
          onChange?.({
            id: res.id,
            ...value,
          });
          setIsEditing(false);
        } else {
          message.error('Failed to add disclaimer');
        }
      }
    } catch {

    } finally {
      setLoading(false);
    }
  };

  return (
    <ConfigurableCard
      header={ {
        title: 'General Information',
        extra: <div style={ { display: 'flex', alignItems: 'center', gap: 8 } }>
          {
            isEditing ? <>
              <Button onClick={cancel}>
                Cancel
              </Button>
              <Button type="primary" onClick={ handleSave } loading={ loading }>
                Save
              </Button>
            </> : (
              <Button onClick={ () => setIsEditing(true) }>
                Edit
              </Button>
            )
          }
        </div>,
      } }
      className={ style.sectionCard }
    >
      <Form
        form={ form }
        layout="vertical"
        initialValues={ value }
      >
        <Form.Item required name="name" label="Disclaimer Name" rules={ validationRules.disclaimerName }>
          <FormInput isEditing={ isEditing } textChildren={ (value) => {
            return <>{ value }</>
          } }>
            <Input/>
          </FormInput>
        </Form.Item>

        <Form.Item required name="description" label="Disclaimer Description"
          rules={ validationRules.disclaimerDescription }>
          <FormInput
            isEditing={ isEditing }
            textChildren={ (value) => {
              return <>{ value }</>
            } }>
            <TextArea
              placeholder="Enter display description"
              rows={ 4 }
            />
          </FormInput>
        </Form.Item>

        <Form.Item name="status" label="Status" valuePropName="checked">
          <FormInput isEditing={ isEditing } textChildren={ (value) => {
            return <>{ value ? 'Active' : 'Inactive' }</>
          } }>
            <Switch checkedChildren="Active" unCheckedChildren="Inactive"/>
          </FormInput>
        </Form.Item>

        <Form.Item label="Legal Approval Document" name="approvalFile"
          extra="This file in only for internal reference.">
          <FormInput isEditing={ isEditing } textChildren={ (value) => {
            return <>{ value ? <UploadFile
              mode="view"
            /> : 'No file uploaded' }</>
          } }>
            <UploadFile
              mode="edit"
            />
          </FormInput>
        </Form.Item>
      </Form>
    </ConfigurableCard>
  );
};

export default GeneralInformation;
