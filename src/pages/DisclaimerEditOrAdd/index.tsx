import { getCountry } from '@classification/admin-solution';
import {
  Button,
  message,
  Result,
  Spin,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';
import {
  useSearchParams,
} from 'react-router-dom';

import { getDisclaimerWithCond } from 'src/api/disclaimer';
import type {
  disclaimer} from 'src/api/disclaimer/disclaimer';
import {
  getAttrList,
} from 'src/api/globalAttribute';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import { getGlobalCategoryList } from 'src/api/uploadAdmin';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import AdvancedSettings from './components/AdvancedSettings';
import GeneralInformation from './components/GeneralInformation';
import RuleModal from './components/RuleModal';
import RuleSummary from './components/RuleSummary';
import style from './style.module.scss';

const { Title } = Typography;





const DisclaimerEditOrAdd: React.FC = () => {
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const isEdit = !!id;

  const [disclaimerData, setDisclaimerData] = useState<disclaimer.IDisclaimer>();
  const [pageStatus, setPageStatus] = useState<'loading' | 'error' | 'success'>(isEdit ? 'loading' : 'success');

  // 高级规则状态 (不在表单中，单独管理)
  const [advancedRules, setAdvancedRules] = useState<disclaimer.IUpdateDisclaimerConditionRequest[]>([]);

  // UI状态
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [ruleModalMode, setRuleModalMode] = useState<'add' | 'edit'>('add');
  const [editingRule, setEditingRule] = useState<disclaimer.IUpdateDisclaimerConditionRequest | undefined>();

  // 动态数据状态
  const [availableCategories, setAvailableCategories] = useState<uploadAdmin.ICategory[]>([]);
  const [categoriesMap, setCategoriesMap] = useState<Map<number, uploadAdmin.ICategory>>(new Map());
  const [categoryAttributes, setCategoryAttributes] = useState<Map<number, globalAttribute.IGlobalAttr[]>>(new Map());
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // 初始化数据
  useEffect(() => {
    // 加载可用分类
    loadAvailableCategories();

    if (isEdit) {
      loadDisclaimerData();
    }
  }, []);

  // 构建分类 Map
  const buildCategoriesMap = (categories: uploadAdmin.ICategory[]): Map<number, uploadAdmin.ICategory> => {
    const map = new Map<number, uploadAdmin.ICategory>();

    const addToMap = (cats: uploadAdmin.ICategory[]) => {
      for (const cat of cats) {
        if (cat.catId) {
          map.set(cat.catId, cat);
        }
        if (cat.subCategories) {
          addToMap(cat.subCategories);
        }
      }
    };

    addToMap(categories);
    return map;
  };

  // 加载可用分类
  const loadAvailableCategories = async () => {
    setCategoriesLoading(true);
    try {
      const response = await getGlobalCategoryList({
        region: getCountry(),
      });
      if (response?.cats) {
        setAvailableCategories(response.cats);
        // 同时构建 categoriesMap
        const map = buildCategoriesMap(response.cats);
        setCategoriesMap(map);
      }
    } catch (error) {
      message.error('Failed to load categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 根据分类加载属性
  const loadCategoryAttributes = async (id: number) => {
    // 检查是否已经加载过该分类的属性
    if (categoryAttributes.has(id)) {
      return;
    }

    try {
      const response = await getAttrList({
        globalCatId: id,
        region: getCountry(),
      });

      if (response?.data?.length) {
        setCategoryAttributes(prev => new Map(prev).set(id, response.data!));
      } else {
      }
    } catch (error) {
    }
  };

  // 加载免责声明数据
  const loadDisclaimerData = async () => {
    try {
     const res = await getDisclaimerWithCond({
        disclaimerId: Number(id),
      });
     if (res?.disclaimerWithCond?.disclaimer) {
       setDisclaimerData(res.disclaimerWithCond.disclaimer);
       setAdvancedRules(res.disclaimerWithCond.conditionInfo?.disclaimerConditionList?.map(c => {
         const {id, mtime, ctime, ...data} = c;
         return {
           ...data,
           conditionId: id,
         }
       }) || []);
       setPageStatus('success');
     } else {
       setPageStatus('error');
     }
    } catch (error) {
      message.error('Failed to load disclaimer data');
      setPageStatus('error');
    }
  };

  // 添加高级规则
  const addAdvancedRule = () => {
    setRuleModalMode('add');
    setEditingRule(undefined);
    setRuleModalVisible(true);
  };

  // 编辑规则
  const editAdvancedRule = (rule: disclaimer.IUpdateDisclaimerConditionRequest) => {
    setRuleModalMode('edit');
    setEditingRule(rule);
    setRuleModalVisible(true);
  };

  // 处理规则Modal确认
  const handleRuleModalConfirm = (rule: disclaimer.IUpdateDisclaimerConditionRequest) => {
    if (ruleModalMode === 'add') {
      // 添加新规则
      const tempId = Date.now();
      const newRule = {
        ...rule,
        conditionId: tempId,
      };

      setAdvancedRules(prev => [...prev, newRule]);
      message.success('Rule added successfully');
    } else {
      // 编辑现有规则
      setAdvancedRules(prev =>
        prev.map(existingRule =>
          existingRule.conditionId === rule.conditionId ? rule : existingRule
        )
      );

      message.success('Rule updated successfully');
    }

    setRuleModalVisible(false);
    setEditingRule(undefined);
  };

  // 获取已使用的category IDs
  const getUsedCategoryIds = (): number[] => {
    return advancedRules.map(rule => rule.catId!).filter(Boolean);
  };

  // 删除高级规则
  const removeAdvancedRule = (conditionId: number, index: number) => {
    setAdvancedRules(prev => prev.filter(rule => rule.conditionId !== conditionId));
  };

  // 更新高级规则
  const updateAdvancedRule = (conditionId: number, index: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => {
    setAdvancedRules(prev =>
      prev.map(rule => {
        if (rule.conditionId === conditionId) {
          // 如果更新的是分类，清空所有属性规则
          if (updates.catId && updates.catId !== rule.catId) {
            return {
              ...rule,
              ...updates,
              attrConditionInfo: {
                attrConditionList: [], // 清空属性规则
              },
            };
          }
          return { ...rule, ...updates };
        }
        return rule;
      })
    );
  };

  // 重试加载数据
  const retryLoadData = () => {
    setPageStatus('loading');
    if (isEdit) {
      loadDisclaimerData();
    } else {
      setPageStatus('success');
    }
  };

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className={style.container}>
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <Spin size="large" tip="Loading disclaimer data..." />
      </div>
    </div>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <div className={style.container}>
      <Result
        status="error"
        title="Failed to Load Data"
        subTitle="Sorry, there was an error loading the disclaimer data. Please try again."
        extra={[
          <Button type="primary" key="retry" onClick={retryLoadData}>
            Try Again
          </Button>,
          <Button key="back" onClick={() => window.history.back()}>
            Go Back
          </Button>,
        ]}
      />
    </div>
  );

  // 渲染成功状态（正常界面）
  const renderSuccessState = () => (
    <div className={style.container}>
      {/* Header */}
      <div className={style.header}>
        <Title level={2}>
          {isEdit ? 'Edit' : 'Add'} Disclaimer — New Disclaimer
        </Title>
      </div>

      {/* General Information */}
      <GeneralInformation
        value={disclaimerData}
        onChange={setDisclaimerData}
      />

      {/* Advanced Settings*/}
      <AdvancedSettings
        disclaimer={disclaimerData}
        advancedRules={advancedRules}
        availableCategories={availableCategories}
        categoriesMap={categoriesMap}
        categoryAttributes={categoryAttributes}
        categoriesLoading={categoriesLoading}
        onAddRule={addAdvancedRule}
        onUpdateRule={updateAdvancedRule}
        onDeleteRule={removeAdvancedRule}
        onCategoryChange={loadCategoryAttributes}
        onEditRule={editAdvancedRule}
      />

      {/* Rule Summary - Only show in Advanced Settings step */}
      <RuleSummary
        advancedRules={advancedRules}
        availableCategories={availableCategories}
        categoryAttributes={categoryAttributes}
      />

      {/* Rule Modal */}
      <RuleModal
        visible={ruleModalVisible}
        onCancel={() => {
          setRuleModalVisible(false);
          setEditingRule(undefined);
        }}
        onConfirm={handleRuleModalConfirm}
        availableCategories={availableCategories}
        categoryAttributes={categoryAttributes}
        categoriesLoading={categoriesLoading}
        usedCategoryIds={getUsedCategoryIds()}
        onCategoryChange={loadCategoryAttributes}
        editingRule={editingRule}
        mode={ruleModalMode}
      />
    </div>
  );

  // 根据页面状态渲染不同界面
  switch (pageStatus) {
    case 'loading':
      return renderLoadingState();
    case 'error':
      return renderErrorState();
    case 'success':
    default:
      return renderSuccessState();
  }
};

export default DisclaimerEditOrAdd;

